* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5rem;
    color: #4a5568;
    margin-bottom: 10px;
    text-align: center;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 0.9rem;
}

.status {
    padding: 5px 12px;
    border-radius: 20px;
    font-weight: 600;
    background: #e2e8f0;
    color: #4a5568;
}

.status.saving {
    background: #fef5e7;
    color: #d69e2e;
}

.status.saved {
    background: #f0fff4;
    color: #38a169;
}

.status.error {
    background: #fed7d7;
    color: #e53e3e;
}

.last-saved {
    color: #718096;
    font-size: 0.8rem;
}

.users-online {
    color: #4a5568;
    font-weight: 500;
}

main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.notepad-container {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

#notepad {
    width: 100%;
    height: 500px;
    border: none;
    outline: none;
    padding: 30px;
    font-size: 16px;
    line-height: 1.6;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    resize: none;
    color: #2d3748;
}

#notepad::placeholder {
    color: #a0aec0;
    font-style: italic;
}

footer {
    margin-top: 20px;
    text-align: center;
}

.footer-info {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 10px 20px;
    border-radius: 25px;
    color: white;
    font-size: 0.9rem;
    display: inline-block;
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    color: white;
}

.loading-overlay.hidden {
    display: none;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification system */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4299e1;
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.hidden {
    display: none;
}

.notification.success {
    background: #38a169;
}

.notification.error {
    background: #e53e3e;
}

.notification.warning {
    background: #d69e2e;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .status-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    #notepad {
        height: 400px;
        padding: 20px;
        font-size: 14px;
    }
    
    .notification {
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }
    
    .notification.show {
        transform: translateY(0);
    }
}

/* Smooth transitions */
.status, .notification {
    transition: all 0.3s ease;
}

#notepad {
    transition: box-shadow 0.3s ease;
}

#notepad:focus {
    box-shadow: inset 0 0 0 2px #4299e1;
}
