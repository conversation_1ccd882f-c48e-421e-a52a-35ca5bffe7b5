// Database configuration
const apiKey = 'rtdb_13b65cff73aa40c19433b3fbc16b829e';
const baseUrl = 'https://a148-2409-40f2-128-7994-2938-6033-4e79-d9a1.ngrok-free.app/api';
const notepadPath = '/notepad/content';

// DOM elements
const notepad = document.getElementById('notepad');
const status = document.getElementById('status');
const lastSaved = document.getElementById('last-saved');
const usersOnline = document.getElementById('users-online');
const loadingOverlay = document.getElementById('loading-overlay');
const notification = document.getElementById('notification');

// State management
let lastSavedContent = '';
let saveTimeout = null;
let syncInterval = null;
let isInitialized = false;
let userCount = 1;
let lastSyncTime = Date.now();

// Database functions
async function readData(path = '') {
    try {
        const response = await fetch(`${baseUrl}/data${path}`, {
            headers: { 
                'X-API-Key': apiKey,
                'ngrok-skip-browser-warning': 'true'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('Read error:', error);
        throw error;
    }
}

async function writeData(path, data) {
    try {
        const response = await fetch(`${baseUrl}/data${path}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': apiKey,
                'ngrok-skip-browser-warning': 'true'
            },
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('Write error:', error);
        throw error;
    }
}

// UI functions
function updateStatus(text, type = 'default') {
    status.textContent = text;
    status.className = `status ${type}`;
}

function updateLastSaved() {
    const now = new Date();
    lastSaved.textContent = `Last saved: ${now.toLocaleTimeString()}`;
}

function updateUsersOnline(count) {
    userCount = count;
    usersOnline.textContent = `👤 ${count} user${count !== 1 ? 's' : ''} online`;
}

function showNotification(message, type = 'info') {
    notification.textContent = message;
    notification.className = `notification ${type}`;
    notification.classList.remove('hidden');
    notification.classList.add('show');
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.classList.add('hidden');
        }, 300);
    }, 3000);
}

function hideLoadingOverlay() {
    loadingOverlay.classList.add('hidden');
}

// Core functionality
async function saveNotepad() {
    const content = notepad.value;
    
    if (content === lastSavedContent) {
        return; // No changes to save
    }
    
    try {
        updateStatus('Saving...', 'saving');
        
        const saveData = {
            content: content,
            timestamp: Date.now(),
            lastModified: new Date().toISOString()
        };
        
        await writeData(notepadPath, saveData);
        
        lastSavedContent = content;
        updateStatus('Saved', 'saved');
        updateLastSaved();
        
        // Reset status after 2 seconds
        setTimeout(() => {
            updateStatus('Ready');
        }, 2000);
        
    } catch (error) {
        console.error('Save failed:', error);
        updateStatus('Save failed', 'error');
        showNotification('Failed to save changes. Please check your connection.', 'error');
    }
}

async function loadNotepad() {
    try {
        const data = await readData(notepadPath);
        
        if (data && data.content !== undefined) {
            // Only update if content is different to avoid cursor jumping
            if (notepad.value !== data.content) {
                const cursorPosition = notepad.selectionStart;
                notepad.value = data.content;
                lastSavedContent = data.content;
                
                // Restore cursor position if possible
                if (cursorPosition <= data.content.length) {
                    notepad.setSelectionRange(cursorPosition, cursorPosition);
                }
                
                if (isInitialized) {
                    showNotification('Content updated by another user', 'info');
                }
            }
        }
        
        return true;
    } catch (error) {
        console.error('Load failed:', error);
        if (!isInitialized) {
            updateStatus('Connection failed', 'error');
            showNotification('Failed to connect to database. Please refresh the page.', 'error');
        }
        return false;
    }
}

async function syncWithServer() {
    try {
        // Update user activity
        await writeData('/notepad/users/activity', {
            timestamp: Date.now(),
            userAgent: navigator.userAgent.substring(0, 50)
        });
        
        // Load latest content
        await loadNotepad();
        
        // Simulate user count (in a real app, you'd track this properly)
        const randomUsers = Math.floor(Math.random() * 3) + 1;
        updateUsersOnline(randomUsers);
        
        lastSyncTime = Date.now();
        
    } catch (error) {
        console.error('Sync failed:', error);
        // Don't show error notifications for sync failures to avoid spam
    }
}

// Event listeners
notepad.addEventListener('input', () => {
    // Clear existing timeout
    if (saveTimeout) {
        clearTimeout(saveTimeout);
    }
    
    // Set new timeout for auto-save
    saveTimeout = setTimeout(saveNotepad, 2000);
    
    // Update status to show typing
    if (notepad.value !== lastSavedContent) {
        updateStatus('Typing...', 'saving');
    }
});

notepad.addEventListener('blur', () => {
    // Save immediately when user leaves the textarea
    if (saveTimeout) {
        clearTimeout(saveTimeout);
    }
    saveNotepad();
});

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Ctrl+S or Cmd+S to save
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        saveNotepad();
    }
});

// Initialize the application
async function initialize() {
    try {
        updateStatus('Connecting...', 'saving');
        
        // Load initial content
        const loaded = await loadNotepad();
        
        if (loaded) {
            updateStatus('Ready');
            hideLoadingOverlay();
            isInitialized = true;
            
            // Start periodic sync
            syncInterval = setInterval(syncWithServer, 5000);
            
            // Focus on notepad
            notepad.focus();
            
            showNotification('Connected! Your notes will sync in realtime.', 'success');
        } else {
            throw new Error('Failed to load initial content');
        }
        
    } catch (error) {
        console.error('Initialization failed:', error);
        updateStatus('Connection failed', 'error');
        showNotification('Failed to initialize. Please refresh the page.', 'error');
        
        // Hide loading overlay even on error
        setTimeout(hideLoadingOverlay, 3000);
    }
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (syncInterval) {
        clearInterval(syncInterval);
    }
    if (saveTimeout) {
        clearTimeout(saveTimeout);
    }
});

// Handle online/offline status
window.addEventListener('online', () => {
    showNotification('Connection restored', 'success');
    syncWithServer();
});

window.addEventListener('offline', () => {
    showNotification('Connection lost. Changes will be saved when online.', 'warning');
    updateStatus('Offline', 'error');
});

// Start the application
document.addEventListener('DOMContentLoaded', initialize);
