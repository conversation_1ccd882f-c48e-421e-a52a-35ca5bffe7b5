<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Realtime Notepad</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>📝 Realtime Notepad</h1>
            <div class="status-bar">
                <span id="status" class="status">Ready</span>
                <span id="last-saved" class="last-saved"></span>
                <span id="users-online" class="users-online">👤 1 user online</span>
            </div>
        </header>
        
        <main>
            <div class="notepad-container">
                <textarea 
                    id="notepad" 
                    placeholder="Start typing your notes here... Changes will be saved automatically and synced in realtime!"
                    spellcheck="true"
                ></textarea>
            </div>
        </main>
        
        <footer>
            <div class="footer-info">
                <span>Auto-saves every 2 seconds • Syncs with other users in realtime</span>
            </div>
        </footer>
    </div>

    <!-- Loading overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>Connecting to realtime database...</p>
    </div>

    <!-- Notification system -->
    <div id="notification" class="notification hidden"></div>

    <script src="script.js"></script>
</body>
</html>
